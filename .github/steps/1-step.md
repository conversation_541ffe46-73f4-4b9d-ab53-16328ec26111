## Step 1: Enable GitHub Pages

### 📖 Theory: What is GitHub Pages?

GitHub Pages lets you turn your repository into a website. This is a great way to share your project, resume, portfolio, documentation, or even a blog with the world.

When you enable GitHub Pages on a repository, GitHub takes the content that's on the main branch and publishes a website based on its contents.

> [!NOTE]
> Learn more in the [GitHub Pages documentation](https://docs.github.com/en/pages/getting-started-with-github-pages/about-github-pages).

### ⌨️ Activity: Enable GitHub Pages



1. Open this repository in a new browser tab so you can work on the steps while you read the instructions in this tab.
1. Under your repository name, click **Settings**.
1. Click **Pages** in the **Code and automation** section.
1. Ensure **Deploy from a branch** is selected from the **Source** drop-down menu, and then select `main` from the **Branch** drop-down menu.
1. Click the **Save** button.

1. With GitHub Pages enabled <PERSON> will be preparing the next step in this exercise!


<details>
<summary>Having trouble? 🤷</summary><br/>

- Turning on GitHub Pages creates a deployment of your repository. GitHub Actions may take up to a minute to respond while waiting for the deployment. Future steps will be about 20 seconds; this step is slower.

</details>
