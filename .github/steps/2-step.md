## Step 2: Customize your homepage

Good job! You turned on GitHub Pages! :tada:

You can see the link to your website at the top of the [Pages](https://github.com/{{full_repo_name}}/settings/pages) section of your repository settings _(you may need to refresh it)_

> [!TIP]
> Keep your GitHub Pages [website](https://{{login}}.github.io/{{repo_name}}/) open in a separate browser tab and keep it handy!
>
> As you progress through this exercise, you'll see your changes reflected on your live site.

### 📖 Theory: Customizing your homepage

You can customize your homepage by adding content to  `index.md` file. As you commit it to the `main` branch your website will be updated to display your personalized content!

### ⌨️ Activity: Create your homepage

1. Browse to the `index.md` file in the `main` branch.
1. In the upper right corner, open the file editor.
1. Type the content you want on your homepage. You can use Markdown formatting on this page.
1. (optional) You can also modify `title:` or leave it as it is.
1. Commit your changes to the `main` branch.
1. As you commit your changes Mona will prepare the next step in this exercise!


<details>
<summary>Having trouble? 🤷</summary><br/>

- Make sure you are editing the `index.md` file in the `main` branch.

</details>
