name: Step 0 # Start Exercise

on:
  push:
    branches:
      - main

permissions:
  contents: write
  actions: write
  issues: write

env:
  STEP_1_FILE: ".github/steps/1-step.md"

jobs:
  start_exercise:
    if: |
      !github.event.repository.is_template
    name: Start Exercise
    uses: skills/exercise-toolkit/.github/workflows/start-exercise.yml@v0.4.0
    with:
      exercise-title: "GitHub Pages"
      intro-message: "Create a site or blog from your GitHub repositories with GitHub Pages."

  create_initial_files:
    name: Create initial files
    runs-on: ubuntu-latest
    needs: [start_exercise]

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: main

      - name: Create empty _config.yml file
        run: |
          touch _config.yml

      - name: Create index.md file
        run: |
          cat > index.md << EOF
          ---
          title: Welcome to my blog!
          ---
          EOF
      - name: Commit files
        uses: EndBug/add-and-commit@v9
        with:
          add: |
            - _config.yml
            - index.md
          message: 'Create config and homepages files'
          default_author: github_actions

  post_next_step_content:
    name: Post next step content
    runs-on: ubuntu-latest
    needs: [start_exercise]
    env:
      ISSUE_URL: ${{ needs.start_exercise.outputs.issue-url }}

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Get response templates
        uses: actions/checkout@v4
        with:
          repository: skills/exercise-toolkit
          path: exercise-toolkit
          ref: v0.4.0

      - name: Build comment - add step content
        id: build-comment
        uses: skills/action-text-variables@v2
        with:
          template-file: ${{ env.STEP_1_FILE }}
          template-vars: |
            login: ${{ github.actor }}
            full_repo_name: ${{ github.repository }}

      - name: Create comment - add step content
        run: |
          gh issue comment "$ISSUE_URL" \
            --body "$ISSUE_BODY"
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          ISSUE_BODY: ${{ steps.build-comment.outputs.updated-text }}

      - name: Create comment - watching for progress
        run: |
          gh issue comment "$ISSUE_URL" \
            --body-file "exercise-toolkit/markdown-templates/step-feedback/watching-for-progress.md"
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Enable next step workflow
        run: |
          gh workflow enable "Step 1"
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
