name: Step 2

on:
  push:
    branches:
      - main
    paths:
      - index.md

permissions:
  contents: read
  actions: write
  issues: write

env:
  STEP_3_FILE: ".github/steps/3-step.md"

jobs:
  find_exercise:
    name: Find Exercise Issue
    uses: skills/exercise-toolkit/.github/workflows/find-exercise-issue.yml@v0.4.0


  post_next_step_content:
    name: Post next step content
    needs: [find_exercise]
    runs-on: ubuntu-latest
    env:
      ISSUE_URL: ${{ needs.find_exercise.outputs.issue-url }}

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Get response templates
        uses: actions/checkout@v4
        with:
          repository: skills/exercise-toolkit
          path: exercise-toolkit
          ref: v0.4.0

      - name: Build comment - add step content
        id: build-comment
        uses: skills/action-text-variables@v2
        with:
          template-file: ${{ env.STEP_3_FILE }}
          template-vars: |
            login: ${{ github.actor }}

      - name: Create comment - add step content
        run: |
          gh issue comment "$ISSUE_URL" \
            --body "$ISSUE_BODY"
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          ISSUE_BODY: ${{ steps.build-comment.outputs.updated-text }}

      - name: Create comment - watching for progress
        run: |
          gh issue comment "$ISSUE_URL" \
            --body-file exercise-toolkit/markdown-templates/step-feedback/watching-for-progress.md
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Disable current workflow and enable next one
        run: |
          gh workflow disable "${{github.workflow}}"
          gh workflow enable "Step 3"
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

