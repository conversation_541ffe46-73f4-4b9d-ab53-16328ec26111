name: Step 3

on:
  push:
    branches:
      - main
    paths:
      - _config.yml

permissions:
  contents: read
  actions: write
  issues: write

env:
  STEP_4_FILE: ".github/steps/4-step.md"

jobs:
  find_exercise:
    name: Find Exercise Issue
    uses: skills/exercise-toolkit/.github/workflows/find-exercise-issue.yml@v0.4.0

  check_step_work:
    name: Check step work
    runs-on: ubuntu-latest
    needs: [find_exercise]
    env:
      ISSUE_URL: ${{ needs.find_exercise.outputs.issue-url }}

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Get response templates
        uses: actions/checkout@v4
        with:
          repository: skills/exercise-toolkit
          path: exercise-toolkit
          ref: v0.4.0

      - name: Update comment - checking work
        run: |
          gh issue comment "$ISSUE_URL" \
            --body-file exercise-toolkit/markdown-templates/step-feedback/checking-work.md \
            --edit-last
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Check for theme minima in _config.yml
        id: check-theme-minima
        continue-on-error: true
        uses: skills/action-keyphrase-checker@v1
        with:
          text-file: _config.yml
          keyphrase: "theme: minima"

      - name: Build message - step results
        id: build-message-step-results
        uses: skills/action-text-variables@v2
        with:
          template-file: exercise-toolkit/markdown-templates/step-feedback/step-results-table.md
          template-vars: |
            step_number: 3
            passed: ${{ !contains(steps.*.outcome, 'failure') }}
            results_table:
              - description: "Looked for minima theme added to the configuration file"
                passed: ${{ steps.check-theme-minima.outcome == 'success' }}

      - name: Create comment - step results
        run: |
          gh issue comment "$ISSUE_URL" \
            --body "$COMMENT_BODY" \
            --edit-last
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          COMMENT_BODY: ${{ steps.build-message-step-results.outputs.updated-text }}

      - name: Fail job if not all checks passed
        if: contains(steps.*.outcome, 'failure')
        run: exit 1

      - name: Build message - step finished
        id: build-message-step-finish
        uses: skills/action-text-variables@v2
        with:
          template-file: exercise-toolkit/markdown-templates/step-feedback/step-finished-prepare-next-step.md
          template-vars: |
            next_step_number: 4

      - name: Update comment - step finished
        run: |
          gh issue comment "$ISSUE_URL" \
            --body "$ISSUE_BODY"
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          ISSUE_BODY: ${{ steps.build-message-step-finish.outputs.updated-text }}

  post_next_step_content:
    name: Post next step content
    needs: [find_exercise, check_step_work]
    runs-on: ubuntu-latest
    env:
      ISSUE_URL: ${{ needs.find_exercise.outputs.issue-url }}

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Get response templates
        uses: actions/checkout@v4
        with:
          repository: skills/exercise-toolkit
          path: exercise-toolkit
          ref: v0.4.0

      - name: Create comment - add step content
        run: |
          gh issue comment "$ISSUE_URL" \
            --body-file "$STEP_4_FILE"
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Create comment - watching for progress
        run: |
          gh issue comment "$ISSUE_URL" \
            --body-file exercise-toolkit/markdown-templates/step-feedback/watching-for-progress.md
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Disable current workflow and enable next one
        run: |
          gh workflow disable "${{github.workflow}}"
          gh workflow enable "Step 4"
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
