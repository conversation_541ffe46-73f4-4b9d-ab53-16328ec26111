name: Step 4

on:
  push:
    branches:
      - main
    paths:
      - _posts/*.md

permissions:
  contents: write
  actions: write
  issues: write

env:
  REVIEW_FILE: ".github/steps/x-review.md"

jobs:
  find_exercise:
    name: Find Exercise Issue
    uses: skills/exercise-toolkit/.github/workflows/find-exercise-issue.yml@v0.4.0

  post_review_content:
    name: Post review content
    needs: [find_exercise]
    runs-on: ubuntu-latest
    env:
      ISSUE_URL: ${{ needs.find_exercise.outputs.issue-url }}

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Create comment - add step content
        run: |
          gh issue comment "$ISSUE_URL" \
            --body-file "$REVIEW_FILE"
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Disable current workflow
        run: gh workflow disable "${{github.workflow}}"
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  # The last workflow in the chain of steps should also finish the exercise.
  finish_exercise:
    name: Finish Exercise
    needs: [find_exercise, post_review_content]
    uses: skills/exercise-toolkit/.github/workflows/finish-exercise.yml@v0.4.0
    with:
      issue-url: ${{ needs.find_exercise.outputs.issue-url }}

