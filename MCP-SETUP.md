# Databutton MCP Setup

This repository is configured with a Databutton MCP (Model Context Protocol) server setup.

## What's Included

- **API Key Storage**: Your Databutton API key is securely stored in `api-keys/databutton-api-key.txt`
- **MCP Configuration**: `mcp-config.json` contains the MCP server configuration
- **Start Script**: `start-databutton-mcp.sh` provides an easy way to start the MCP server
- **Security**: API keys are properly gitignored to prevent accidental commits

## Files Created

```
├── api-keys/
│   └── databutton-api-key.txt     # Your Databutton API key (gitignored)
├── mcp-config.json                # MCP server configuration
├── start-databutton-mcp.sh        # Script to start the MCP server
└── MCP-SETUP.md                   # This documentation
```

## How to Use

### Option 1: Direct Command
```bash
uvx databutton-app-mcp@latest -k api-keys/databutton-api-key.txt
```

### Option 2: Using the Start Script
```bash
./start-databutton-mcp.sh
```

### Option 3: With MCP Client Configuration
Use the `mcp-config.json` file with MCP-compatible clients like:
- <PERSON> Desktop
- Other MCP-compatible applications

## MCP Configuration for Claude Desktop

If you're using Claude Desktop, add this to your Claude Desktop configuration:

```json
{
  "mcpServers": {
    "databutton": {
      "command": "uvx",
      "args": [
        "databutton-app-mcp@latest",
        "-k",
        "/workspaces/TerribleTrajectory/api-keys/databutton-api-key.txt"
      ]
    }
  }
}
```

## Security Notes

- The API key file is automatically gitignored
- Never commit API keys to version control
- The API key file is stored in the `api-keys/` directory which is excluded from git

## Troubleshooting

1. **Permission Error**: Make sure the start script is executable:
   ```bash
   chmod +x start-databutton-mcp.sh
   ```

2. **API Key Not Found**: Ensure the API key file exists:
   ```bash
   ls -la api-keys/databutton-api-key.txt
   ```

3. **uvx Not Found**: Install uv first:
   ```bash
   pipx install uv
   ```

## What is MCP?

Model Context Protocol (MCP) is a standard for connecting AI assistants to external tools and data sources. The Databutton MCP server allows AI assistants to interact with your Databutton applications and data.
