# Scriptrix MCP Tools Guide

Your **Scriptrix** app provides powerful MCP tools for targeted development and precise UI/content fixes.

## 🛠️ Available Tools

### 1. `get_project` - Inspect Current State
**Purpose**: Get the current state of a project for analysis
```javascript
// Example usage
await get_project({project_name: "my-pilot"})
```

### 2. `update_project` - Targeted Modifications  
**Purpose**: Make precise updates to specific modules/content
```javascript
// Example usage
await update_project({
  project_name: "my-pilot", 
  modules: {
    plot: {
      trajectory: "Updated character arc",
      characters: "Refined character list"
    }
  }
})
```

### 3. `export_project_json` - Export for Analysis
**Purpose**: Export project data as JSON for backup/analysis
```javascript
// Example usage
await export_project_json({project_name: "my-pilot"})
```

## 🎯 Benefits for Development

✅ **Precision** - Target exact fields/modules  
✅ **Speed** - No full app rebuilds needed  
✅ **Batch Operations** - Multiple fixes at once  
✅ **State Inspection** - See current data before changes  
✅ **Rollback Friendly** - Easy to revert specific changes  

## 🔧 Setup Options

### Option 1: Claude <PERSON>

1. **Install Claude Desktop** from https://claude.ai/download

2. **Configure MCP**: Open Claude Desktop → Settings → Developer → Edit Config

3. **Add this configuration**:
```json
{
  "mcpServers": {
    "scriptrix": {
      "command": "uvx",
      "args": [
        "databutton-app-mcp@latest",
        "-k",
        "/workspaces/TerribleTrajectory/api-keys/databutton-api-key.txt"
      ]
    }
  },
  "globalShortcut": ""
}
```

4. **Restart Claude Desktop**

### Option 2: VS Code with Continue.dev

1. **Install Continue Extension**:
   - Open VS Code Extensions (Ctrl+Shift+X)
   - Search for "Continue"
   - Install the Continue extension

2. **Configure MCP**: Add to VS Code settings.json:
```json
{
  "continue.mcp.servers": [
    {
      "name": "scriptrix-fixes",
      "command": "uvx",
      "args": [
        "databutton-app-mcp@latest",
        "-k",
        "api-keys/databutton-api-key.txt"
      ],
      "cwd": "/workspaces/TerribleTrajectory",
      "description": "Scriptrix targeted fixes and updates"
    }
  ]
}
```

## 🚀 Example Use Cases

### Content Fixes
```javascript
// Fix specific plot elements
await update_project({
  project_name: "sci-fi-pilot",
  modules: {
    plot: {
      trajectory: "Hero discovers alien technology, must choose between power and humanity",
      conflict: "Internal struggle between ambition and morality"
    }
  }
})
```

### Character Updates
```javascript
// Update character details
await update_project({
  project_name: "comedy-pilot",
  modules: {
    characters: {
      protagonist: "Sarah Chen - Ambitious food blogger with social anxiety",
      antagonist: "Marcus Vale - Rival chef with hidden insecurities"
    }
  }
})
```

### Bulk Project Analysis
```javascript
// Export multiple projects for comparison
const project1 = await export_project_json({project_name: "pilot-v1"})
const project2 = await export_project_json({project_name: "pilot-v2"})
// Compare and analyze differences
```

### State Inspection Before Changes
```javascript
// Check current state before making changes
const currentState = await get_project({project_name: "my-pilot"})
console.log("Current plot:", currentState.modules.plot)

// Make targeted update
await update_project({
  project_name: "my-pilot",
  modules: {
    plot: {
      ...currentState.modules.plot,
      trajectory: "Enhanced version of existing trajectory"
    }
  }
})
```

## 💡 AI Assistant Commands

Once set up with Claude Desktop or VS Code, you can ask:

### Project Management
- "Get the current state of my sci-fi pilot project"
- "Export the comedy pilot as JSON for backup"
- "Show me what's in the project called 'mystery-series'"

### Content Updates
- "Update the plot trajectory for my pilot to focus more on character development"
- "Refine the character descriptions in the drama pilot"
- "Add a new subplot about family dynamics to the existing plot"

### Batch Operations
- "Update all my pilot projects to include budget information"
- "Export all projects as JSON files"
- "Check the current state of all active projects"

### Development Workflow
- "Before I make changes, show me the current plot structure"
- "Update just the character arc without changing other plot elements"
- "Revert the last changes to the comedy pilot's dialogue"

## 🔍 Troubleshooting

### MCP Server Not Connecting
1. Ensure `uv` is installed: `pipx install uv`
2. Check API key file exists: `ls -la api-keys/databutton-api-key.txt`
3. Restart your MCP client (Claude Desktop/VS Code)

### Tools Not Appearing
1. Verify MCP configuration is correct
2. Check that your Databutton app is deployed
3. Ensure MCP is enabled in your Databutton app settings

### Authentication Errors
1. Verify API key is correct and not expired
2. Check file permissions on API key file
3. Ensure the API key file path is absolute in configurations

## 🎬 Next Steps

1. **Set up your preferred MCP client** (Claude Desktop recommended)
2. **Test with simple commands** like `get_project`
3. **Try targeted updates** to specific modules
4. **Use for your development workflow** - inspect, update, export
5. **Integrate into your creative process** for TV pilot development

Your Scriptrix MCP setup is ready for precise, targeted development work! 🚀
