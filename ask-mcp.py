#!/usr/bin/env python3
"""
Simple script to send a specific request to the Databutton MCP server.
This will ask: "Find the main App component and tell me what it renders"
"""

import asyncio
import json
import websockets
import sys
from pathlib import Path

async def ask_mcp_question(question):
    """Send a specific question to the Databutton MCP server."""
    
    # Read the API key
    api_key_path = Path("api-keys/databutton-api-key.txt")
    if not api_key_path.exists():
        print("❌ API key file not found")
        return False
    
    api_key = api_key_path.read_text().strip()
    
    # The WebSocket URL from the --show-uri command
    uri = "wss://api.databutton.com/_projects/9c68a944-711f-4b04-b971-1480fd9f4385/dbtn/prodx/app/mcp/ws"
    
    try:
        print(f"🔗 Connecting to Databutton MCP server...")
        print(f"❓ Question: {question}")
        print("=" * 60)
        
        # Try different authentication methods
        uris_to_try = [
            f"{uri}?api_key={api_key}",
            f"{uri}?token={api_key}",
            uri  # Try without auth first
        ]
        
        for attempt, test_uri in enumerate(uris_to_try, 1):
            try:
                print(f"🔄 Attempt {attempt}: Connecting...")
                
                async with websockets.connect(test_uri, timeout=10) as websocket:
                    print("✅ Connected!")
                    
                    # Send MCP initialization
                    init_message = {
                        "jsonrpc": "2.0",
                        "id": 1,
                        "method": "initialize",
                        "params": {
                            "protocolVersion": "2024-11-05",
                            "capabilities": {
                                "tools": {},
                                "resources": {}
                            },
                            "clientInfo": {
                                "name": "databutton-question-client",
                                "version": "1.0.0"
                            }
                        }
                    }
                    
                    await websocket.send(json.dumps(init_message))
                    response = await asyncio.wait_for(websocket.recv(), timeout=10)
                    
                    init_response = json.loads(response)
                    print("📥 Initialization response:")
                    print(json.dumps(init_response, indent=2))
                    
                    if "result" in init_response:
                        # Get available tools first
                        tools_message = {
                            "jsonrpc": "2.0",
                            "id": 2,
                            "method": "tools/list"
                        }
                        
                        await websocket.send(json.dumps(tools_message))
                        tools_response = await asyncio.wait_for(websocket.recv(), timeout=10)
                        
                        tools_data = json.loads(tools_response)
                        print("\n🛠️  Available tools:")
                        print(json.dumps(tools_data, indent=2))
                        
                        # If we have tools, try to use one that might help with code exploration
                        if "result" in tools_data and "tools" in tools_data["result"]:
                            tools = tools_data["result"]["tools"]
                            
                            if tools:
                                print(f"\n🎯 Found {len(tools)} tools. Trying to use them...")
                                
                                # Try to use the first available tool with our question
                                first_tool = tools[0]
                                tool_name = first_tool.get("name", "unknown")
                                
                                tool_call = {
                                    "jsonrpc": "2.0",
                                    "id": 3,
                                    "method": "tools/call",
                                    "params": {
                                        "name": tool_name,
                                        "arguments": {
                                            "query": question,
                                            "request": question,
                                            "question": question
                                        }
                                    }
                                }
                                
                                await websocket.send(json.dumps(tool_call))
                                tool_response = await asyncio.wait_for(websocket.recv(), timeout=15)
                                
                                tool_result = json.loads(tool_response)
                                print(f"\n🎯 Tool '{tool_name}' response:")
                                print(json.dumps(tool_result, indent=2))
                                
                            else:
                                print("❌ No tools available in this MCP server")
                        
                        return True
                    else:
                        print("❌ Initialization failed")
                        continue
                        
            except websockets.exceptions.ConnectionClosed as e:
                print(f"❌ Connection closed (attempt {attempt}): {e}")
                continue
            except asyncio.TimeoutError:
                print(f"❌ Timeout (attempt {attempt})")
                continue
            except Exception as e:
                print(f"❌ Error (attempt {attempt}): {e}")
                continue
        
        print("❌ All connection attempts failed")
        return False
                
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

async def main():
    """Main function."""
    question = "Find the main App component and tell me what it renders"
    
    print("🤖 Asking Databutton MCP Server")
    print("=" * 50)
    
    success = await ask_mcp_question(question)
    
    if not success:
        print("\n💡 Alternative approach:")
        print("The MCP server might need to be connected through a proper MCP client like Claude Desktop.")
        print("You can:")
        print("1. Set up Claude Desktop with the MCP configuration")
        print("2. Ask Claude: 'Find the main App component and tell me what it renders'")
        print("3. Claude will use your Databutton MCP tools to answer")
    
    return 0 if success else 1

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(result)
    except KeyboardInterrupt:
        print("\n🛑 Interrupted by user")
        sys.exit(1)
