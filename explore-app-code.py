#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to explore the Databutton app's code structure and functionality.
This will help us understand what the app does and how to work with it.
"""

import asyncio
import json
import websockets
import sys
from pathlib import Path

async def explore_app_functionality():
    """Explore what the app does by calling its tools and analyzing responses."""
    
    # Read the API key
    api_key_path = Path("api-keys/databutton-api-key.txt")
    if not api_key_path.exists():
        print("❌ API key file not found")
        return False
    
    api_key = api_key_path.read_text().strip()
    uri = "wss://api.databutton.com/_projects/9c68a944-711f-4b04-b971-1480fd9f4385/dbtn/prodx/app/mcp/ws"
    
    try:
        print("🔍 Exploring Your Databutton App")
        print("=" * 50)
        
        # Try connecting with API key as query parameter
        uri_with_key = f"{uri}?api_key={api_key}"
        
        async with websockets.connect(uri_with_key) as websocket:
            print("✅ Connected to your app!")
            
            # Initialize MCP connection
            init_message = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "initialize",
                "params": {
                    "protocolVersion": "2024-11-05",
                    "capabilities": {"tools": {}},
                    "clientInfo": {"name": "app-explorer", "version": "1.0.0"}
                }
            }
            
            await websocket.send(json.dumps(init_message))
            response = await websocket.recv()
            init_result = json.loads(response)
            
            if "result" not in init_result:
                print("❌ Failed to initialize MCP connection")
                return False
            
            print("✅ MCP connection initialized")
            
            # Get available tools
            tools_message = {"jsonrpc": "2.0", "id": 2, "method": "tools/list"}
            await websocket.send(json.dumps(tools_message))
            tools_response = await websocket.recv()
            tools_data = json.loads(tools_response)
            
            if "result" not in tools_data or "tools" not in tools_data["result"]:
                print("❌ No tools available")
                return False
            
            tools = tools_data["result"]["tools"]
            print(f"\n🛠️  Available Tools ({len(tools)}):")
            print("-" * 40)
            
            for i, tool in enumerate(tools, 1):
                name = tool.get("name", "unknown")
                desc = tool.get("description", "No description")
                print(f"{i}. {name}")
                print(f"   📝 {desc}")
                
                # Show input schema if available
                if "inputSchema" in tool:
                    schema = tool["inputSchema"]
                    if "properties" in schema:
                        props = list(schema["properties"].keys())
                        print(f"   📥 Inputs: {', '.join(props)}")
                print()
            
            # Let's explore what the app does by testing its functionality
            print("🧪 Testing App Functionality")
            print("-" * 40)
            
            # 1. Health check first
            print("1️⃣ Health Check...")
            health_call = {
                "jsonrpc": "2.0", "id": 3, "method": "tools/call",
                "params": {"name": "check_health", "arguments": {}}
            }
            await websocket.send(json.dumps(health_call))
            health_response = await websocket.recv()
            health_result = json.loads(health_response)
            
            if "result" in health_result:
                print("✅ App is healthy!")
                if "content" in health_result["result"]:
                    for content in health_result["result"]["content"]:
                        if content.get("type") == "text":
                            print(f"   📄 {content.get('text', '')}")
            
            # 2. List existing projects to understand the data structure
            print("\n2️⃣ Exploring existing projects...")
            list_call = {
                "jsonrpc": "2.0", "id": 4, "method": "tools/call",
                "params": {"name": "list_projects", "arguments": {}}
            }
            await websocket.send(json.dumps(list_call))
            list_response = await websocket.recv()
            list_result = json.loads(list_response)
            
            if "result" in list_result:
                print("📋 Projects found:")
                if "content" in list_result["result"]:
                    for content in list_result["result"]["content"]:
                        if content.get("type") == "text":
                            text = content.get("text", "")
                            print(f"   📄 {text}")
                            
                            # Try to parse as JSON to understand structure
                            try:
                                if text.strip().startswith('[') or text.strip().startswith('{'):
                                    data = json.loads(text)
                                    if isinstance(data, list) and len(data) > 0:
                                        print(f"   📊 Found {len(data)} projects")
                                        print(f"   🔍 Sample project structure:")
                                        sample = data[0]
                                        for key, value in sample.items():
                                            print(f"      • {key}: {type(value).__name__}")
                            except:
                                pass
            
            # 3. If we have projects, get details of one
            print("\n3️⃣ Getting project details...")
            # We'll try to get project with ID 1 or the first available
            get_call = {
                "jsonrpc": "2.0", "id": 5, "method": "tools/call",
                "params": {"name": "get_project", "arguments": {"project_id": "1"}}
            }
            await websocket.send(json.dumps(get_call))
            get_response = await websocket.recv()
            get_result = json.loads(get_response)
            
            if "result" in get_result:
                print("📄 Project details:")
                if "content" in get_result["result"]:
                    for content in get_result["result"]["content"]:
                        if content.get("type") == "text":
                            print(f"   📄 {content.get('text', '')}")
            
            print("\n🎯 Summary:")
            print("-" * 40)
            print("Your Databutton app appears to be a TV Pilot Project Management System with:")
            print("• Health monitoring")
            print("• Project CRUD operations (Create, Read, Update, Delete)")
            print("• JSON export functionality")
            print("• Structured data storage for TV pilot projects")
            
            print("\n💡 To work on the code:")
            print("• The MCP tools expose your app's API endpoints")
            print("• To modify the actual code, you'd work in the Databutton platform")
            print("• These tools let AI assistants interact with your deployed app")
            print("• You can ask questions like:")
            print("  - 'Create a new TV pilot project with these details...'")
            print("  - 'Show me all projects and their status'")
            print("  - 'Export project data as JSON'")
            
            return True
            
    except Exception as e:
        print(f"❌ Error exploring app: {e}")
        return False

async def main():
    """Main function."""
    success = await explore_app_functionality()
    return 0 if success else 1

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(result)
    except KeyboardInterrupt:
        print("\n🛑 Interrupted by user")
        sys.exit(1)
