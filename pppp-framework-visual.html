<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Complete TV Pilot Framework Visual</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1500px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .main-title {
            font-size: 3em;
            font-weight: bold;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }
        
        .subtitle {
            font-size: 1.2em;
            color: #666;
            margin-bottom: 20px;
        }
        
        .central-hub {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 50px 0;
            position: relative;
        }
        
        .biassociation-core {
            background: linear-gradient(135deg, #ff6b6b, #feca57);
            border-radius: 50%;
            width: 200px;
            height: 200px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            font-weight: bold;
            font-size: 1.2em;
            text-align: center;
            box-shadow: 0 15px 30px rgba(0,0,0,0.3);
            z-index: 10;
            position: relative;
        }
        
        .core-title {
            font-size: 1.4em;
            margin-bottom: 10px;
        }
        
        .core-subtitle {
            font-size: 0.9em;
            opacity: 0.9;
        }
        
        .framework-orbit {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 30px;
            margin: 60px 0;
        }
        
        .framework-section {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-radius: 15px;
            padding: 30px;
            color: white;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            position: relative;
        }
        
        .section-1 { --primary-color: #ff6b6b; --secondary-color: #ee5a52; }
        .section-2 { --primary-color: #4ecdc4; --secondary-color: #44a08d; }
        .section-3 { --primary-color: #45b7d1; --secondary-color: #96c93d; }
        .section-4 { --primary-color: #f9ca24; --secondary-color: #f0932b; }
        
        .section-title {
            font-size: 1.8em;
            font-weight: bold;
            margin-bottom: 15px;
        }
        
        .section-subtitle {
            font-size: 1.1em;
            opacity: 0.9;
            margin-bottom: 20px;
        }
        
        .section-items {
            font-size: 0.95em;
            line-height: 1.5;
        }
        
        .connecting-lines {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 300px;
            height: 300px;
            pointer-events: none;
        }
        
        .line {
            position: absolute;
            background: linear-gradient(45deg, #ff6b6b, #feca57);
            opacity: 0.6;
        }
        
        .line-1 {
            width: 2px;
            height: 80px;
            top: -80px;
            left: 50%;
            transform: translateX(-50%);
        }
        
        .line-2 {
            width: 80px;
            height: 2px;
            right: -80px;
            top: 50%;
            transform: translateY(-50%);
        }
        
        .line-3 {
            width: 2px;
            height: 80px;
            bottom: -80px;
            left: 50%;
            transform: translateX(-50%);
        }
        
        .line-4 {
            width: 80px;
            height: 2px;
            left: -80px;
            top: 50%;
            transform: translateY(-50%);
        }
        
        .character-trajectories {
            margin: 40px 0;
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
        }
        
        .trajectories-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .trajectory-card {
            background: linear-gradient(135deg, var(--traj-color), var(--traj-color-2));
            border-radius: 10px;
            padding: 20px;
            color: white;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .trajectory-title {
            font-size: 1.1em;
            font-weight: bold;
            margin-bottom: 8px;
        }
        
        .trajectory-desc {
            font-size: 0.85em;
            opacity: 0.9;
        }
        
        .traj-1 { --traj-color: #fa7c91; --traj-color-2: #f093fb; }
        .traj-2 { --traj-color: #4facfe; --traj-color-2: #00f2fe; }
        .traj-3 { --traj-color: #43e97b; --traj-color-2: #38f9d7; }
        .traj-4 { --traj-color: #fa709a; --traj-color-2: #fee140; }
        .traj-5 { --traj-color: #a8edea; --traj-color-2: #fed6e3; }
        .traj-6 { --traj-color: #ff9a9e; --traj-color-2: #fecfef; }
        
        .eight-char-section {
            margin: 40px 0;
            text-align: center;
            background: #f1f3f4;
            border-radius: 15px;
            padding: 30px;
        }
        
        .char-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            margin: 20px 0;
        }
        
        .char-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 15px;
            border-radius: 10px;
            font-weight: bold;
            font-size: 0.9em;
        }
        
        .driver { background: linear-gradient(135deg, #ff6b6b, #ee5a52); }
        .passenger { background: linear-gradient(135deg, #4ecdc4, #44a08d); }
        
        .powers-section {
            margin: 20px 0;
            background: linear-gradient(135deg, #f093fb, #f5576c);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        
        .section-title-main {
            font-size: 2em;
            font-weight: bold;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .biassociation-examples {
            background: #fff8e1;
            border-radius: 15px;
            padding: 30px;
            margin: 40px 0;
            border-left: 5px solid #feca57;
        }
        
        .example-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .example-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        
        .example-title {
            font-weight: bold;
            color: #e67e22;
            margin-bottom: 10px;
        }
        
        .integration-flow {
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 40px 0;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .flow-item {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: bold;
            font-size: 0.9em;
        }
        
        .flow-center {
            background: linear-gradient(135deg, #ff6b6b, #feca57);
            padding: 15px 30px;
            font-size: 1.1em;
        }
        
        .plus {
            font-size: 1.5em;
            color: #667eea;
            font-weight: bold;
        }
        
        @media (max-width: 768px) {
            .framework-orbit {
                grid-template-columns: 1fr;
            }
            
            .char-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .integration-flow {
                flex-direction: column;
            }
            
            .plus {
                transform: rotate(90deg);
                margin: 10px 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="main-title">COMPLETE TV PILOT FRAMEWORK</h1>
            <p class="subtitle">Biassociation-Driven Story Development System</p>
            <p class="subtitle">Character-Centered • Emotionally Complex • Structurally Sound</p>
        </div>
        
        <div class="central-hub">
            <div class="biassociation-core">
                <div class="core-title">BIASSOCIATION</div>
                <div class="core-subtitle">Emotional Engine<br>& Story Heart</div>
            </div>
            <div class="connecting-lines">
                <div class="line line-1"></div>
                <div class="line line-2"></div>
                <div class="line line-3"></div>
                <div class="line line-4"></div>
            </div>
        </div>
        
        <div class="framework-orbit">
            <div class="framework-section section-1">
                <div class="section-title">PPPP FRAMEWORK</div>
                <div class="section-subtitle">Plot → Plan → Pattern → Produce</div>
                <div class="section-items">
                    • Character-driven plot development<br>
                    • Strategic structure planning<br>
                    • Visual movement patterns<br>
                    • Scene execution mastery<br>
                    • AI-enhanced production
                </div>
            </div>
            
            <div class="framework-section section-2">
                <div class="section-title">CHARACTER TRAJECTORIES</div>
                <div class="section-subtitle">Six "Terrible" Starting Points</div>
                <div class="section-items">
                    • Terribly Delusional<br>
                    • Terrible Choices<br>
                    • Terrible Flaw<br>
                    • Terribly Duped<br>
                    • Terribly Wrong Impression<br>
                    • Terrible Behavior
                </div>
            </div>
            
            <div class="framework-section section-3">
                <div class="section-title">ENSEMBLE DYNAMICS</div>
                <div class="section-subtitle">8-Character Framework</div>
                <div class="section-items">
                    • 4 Action Drivers<br>
                    • 4 Support Characters<br>
                    • Powers That Be<br>
                    • Thematic character contrast<br>
                    • Complementary trajectories
                </div>
            </div>
            
            <div class="framework-section section-4">
                <div class="section-title">STRATEGIC CONSTRAINTS</div>
                <div class="section-subtitle">Creative Catalysts</div>
                <div class="section-items">
                    • Selective constraints<br>
                    • Enabling constraints<br>
                    • Genre boundaries<br>
                    • Format limitations<br>
                    • Resource parameters
                </div>
            </div>
        </div>
        
        <div class="biassociation-examples">
            <h2 class="section-title-main">Biassociation in Action</h2>
            <div class="example-grid">
                <div class="example-card">
                    <div class="example-title">Story Engine</div>
                    <div>Character thinks they're solving crimes → but misses the real danger happening to loved ones</div>
                </div>
                <div class="example-card">
                    <div class="example-title">Thematic Core</div>
                    <div>When people don't advocate for themselves → they become vulnerable to erasure</div>
                </div>
                <div class="example-card">
                    <div class="example-title">Plot Mechanism</div>
                    <div>Protagonist is always half-right → finds real clues while chasing wrong theories</div>
                </div>
                <div class="example-card">
                    <div class="example-title">Emotional Irony</div>
                    <div>Audience laughs with protagonist → then realizes they missed the danger too</div>
                </div>
            </div>
        </div>
        
        <div class="character-trajectories">
            <h2 class="section-title-main">Six Character Trajectories</h2>
            <div class="trajectories-grid">
                <div class="trajectory-card traj-1">
                    <div class="trajectory-title">TERRIBLY DELUSIONAL</div>
                    <div class="trajectory-desc">Idealism that proves unexpectedly valuable</div>
                </div>
                <div class="trajectory-card traj-2">
                    <div class="trajectory-title">TERRIBLE CHOICES</div>
                    <div class="trajectory-desc">Moral compromises to achieve goals</div>
                </div>
                <div class="trajectory-card traj-3">
                    <div class="trajectory-title">TERRIBLE FLAW</div>
                    <div class="trajectory-desc">Overcoming ingrained negative patterns</div>
                </div>
                <div class="trajectory-card traj-4">
                    <div class="trajectory-title">TERRIBLY DUPED</div>
                    <div class="trajectory-desc">Discovering and responding to deception</div>
                </div>
                <div class="trajectory-card traj-5">
                    <div class="trajectory-title">TERRIBLY WRONG IMPRESSION</div>
                    <div class="trajectory-desc">Finding connection where least expected</div>
                </div>
                <div class="trajectory-card traj-6">
                    <div class="trajectory-title">TERRIBLE BEHAVIOR</div>
                    <div class="trajectory-desc">Becoming what they once judged</div>
                </div>
            </div>
        </div>
        
        <div class="eight-char-section">
            <h2 class="section-title-main">8-Character Ensemble Framework</h2>
            <div class="char-grid">
                <div class="char-card driver">DRIVER 1<br><small>Action Initiator</small></div>
                <div class="char-card driver">DRIVER 2<br><small>Action Initiator</small></div>
                <div class="char-card driver">DRIVER 3<br><small>Action Initiator</small></div>
                <div class="char-card driver">DRIVER 4<br><small>Action Initiator</small></div>
                <div class="char-card passenger">PASSENGER 1<br><small>Responder/Support</small></div>
                <div class="char-card passenger">PASSENGER 2<br><small>Responder/Support</small></div>
                <div class="char-card passenger">PASSENGER 3<br><small>Responder/Support</small></div>
                <div class="char-card passenger">PASSENGER 4<br><small>Responder/Support</small></div>
            </div>
            <div class="powers-section">
                <strong>POWERS THAT BE</strong><br>
                <small>External forces that regulate outcomes and set boundaries</small>
            </div>
        </div>
        
        <div class="integration-flow">
            <div class="flow-item">Character Trajectories</div>
            <div class="plus">+</div>
            <div class="flow-center">BIASSOCIATION</div>
            <div class="plus">+</div>
            <div class="flow-item">PPPP Framework</div>
            <div class="plus">+</div>
            <div class="flow-item">Strategic Constraints</div>
            <div class="plus">=</div>
            <div class="flow-item">Compelling TV Pilot</div>
        </div>
    </div>
</body>
</html>