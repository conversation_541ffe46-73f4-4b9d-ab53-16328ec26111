# Scriptrix MCP Examples

## 🎬 Real-World Usage Examples

### Example 1: <PERSON>reating and Managing a TV Pilot Project

```javascript
// 1. First, check if a project exists
const currentProject = await get_project({project_name: "space-detective"})

// 2. Create/update the project with initial content
await update_project({
  project_name: "space-detective",
  modules: {
    plot: {
      trajectory: "Detective <PERSON> investigates mysterious disappearances on Mars colony, discovers corporate conspiracy",
      genre: "Sci-Fi Mystery",
      logline: "When colonists start vanishing on Mars, a cynical detective uncovers a conspiracy that threatens humanity's future in space"
    },
    characters: {
      protagonist: "<PERSON> - Former Earth cop turned Mars detective, haunted by past case",
      antagonist: "Dr<PERSON> - Colony administrator hiding deadly corporate secrets",
      supporting: "<PERSON><PERSON> engineer who becomes <PERSON>'s reluctant ally"
    },
    structure: {
      act1: "<PERSON> arrives on Mars, first disappearance occurs",
      act2: "Investigation deepens, corporate conspiracy emerges",
      act3: "Final confrontation, truth revealed, colony saved"
    }
  }
})

// 3. Export for backup/sharing
const projectData = await export_project_json({project_name: "space-detective"})
```

### Example 2: Iterative Content Refinement

```javascript
// Get current state
const current = await get_project({project_name: "comedy-pilot"})

// Make targeted improvements
await update_project({
  project_name: "comedy-pilot",
  modules: {
    plot: {
      ...current.modules.plot,
      trajectory: "Enhanced: " + current.modules.plot.trajectory + " - Now with more character-driven humor and emotional depth"
    },
    characters: {
      ...current.modules.characters,
      protagonist: current.modules.characters.protagonist + " - Added quirk: collects vintage lunch boxes"
    }
  }
})
```

### Example 3: Batch Project Management

```javascript
// List of pilot projects to update
const projects = ["sci-fi-pilot", "comedy-pilot", "drama-pilot", "mystery-pilot"]

// Add budget information to all projects
for (const projectName of projects) {
  await update_project({
    project_name: projectName,
    modules: {
      production: {
        budget: "Mid-range TV pilot budget: $2-5M",
        timeline: "6 months pre-production, 3 weeks filming, 8 weeks post",
        locations: "Studio + 2-3 practical locations"
      }
    }
  })
}
```

## 🎯 Targeted Fix Examples

### Fix 1: Character Development Enhancement
```javascript
// Before: Basic character description
// After: Rich, multi-dimensional character

await update_project({
  project_name: "my-pilot",
  modules: {
    characters: {
      protagonist: `
        Name: Alex Rivera
        Age: 32
        Background: Former military medic turned small-town doctor
        Flaw: Struggles with PTSD, avoids emotional connections
        Goal: Wants to heal others while learning to heal themselves
        Arc: From isolated healer to community leader who accepts help
        Quirks: Always carries a worn medical bag, quotes medical textbooks when nervous
      `
    }
  }
})
```

### Fix 2: Plot Structure Refinement
```javascript
// Enhance plot with specific beats and emotional moments
await update_project({
  project_name: "drama-pilot",
  modules: {
    plot: {
      structure: {
        teaser: "Cold open: Mysterious patient arrives during storm",
        act1: "Setup: Alex's routine disrupted, past trauma triggered",
        act2a: "Rising action: Medical crisis reveals town secrets",
        act2b: "Midpoint: Alex must choose between safety and helping",
        act3: "Climax: Medical emergency forces Alex to confront fears",
        tag: "Resolution: Alex begins to open up to community"
      },
      emotional_beats: {
        opening: "Isolation and routine",
        inciting: "Disruption and fear",
        midpoint: "Choice and courage",
        climax: "Confrontation and growth",
        resolution: "Connection and hope"
      }
    }
  }
})
```

### Fix 3: Adding Visual Elements
```javascript
// Add visual storytelling elements
await update_project({
  project_name: "thriller-pilot",
  modules: {
    visual: {
      color_palette: "Cool blues and grays with warm amber highlights for hope",
      cinematography: "Handheld for tension, steady for calm moments",
      key_images: [
        "Rain-soaked streets reflecting neon signs",
        "Close-up of protagonist's hands shaking",
        "Wide shot of empty apartment showing isolation"
      ]
    },
    tone: {
      overall: "Tense but hopeful, grounded realism with thriller elements",
      dialogue: "Natural, overlapping, subtext-heavy",
      pacing: "Slow burn building to intense climax"
    }
  }
})
```

## 🔧 Development Workflow Examples

### Workflow 1: Daily Content Review
```javascript
// Morning routine: Check all active projects
const projects = ["pilot-a", "pilot-b", "pilot-c"]

for (const project of projects) {
  const state = await get_project({project_name: project})
  console.log(`${project} status:`, state.modules.plot?.trajectory || "No plot yet")
}
```

### Workflow 2: Version Control for Content
```javascript
// Before making major changes, backup current state
const backup = await export_project_json({project_name: "important-pilot"})
// Save backup with timestamp
const timestamp = new Date().toISOString().split('T')[0]
// Store backup data...

// Make experimental changes
await update_project({
  project_name: "important-pilot",
  modules: {
    plot: {
      experimental_trajectory: "Trying a completely different approach..."
    }
  }
})

// If changes don't work, can restore from backup
```

### Workflow 3: Collaborative Development
```javascript
// Team member A: Sets up basic structure
await update_project({
  project_name: "team-pilot",
  modules: {
    plot: { trajectory: "Basic story outline..." },
    characters: { protagonist: "Main character concept..." }
  }
})

// Team member B: Adds details
const current = await get_project({project_name: "team-pilot"})
await update_project({
  project_name: "team-pilot",
  modules: {
    ...current.modules,
    dialogue: { sample_scenes: "Key dialogue moments..." },
    themes: { main_theme: "What the story is really about..." }
  }
})
```

## 💡 Pro Tips

### 1. Always Inspect Before Updating
```javascript
// Good practice: Check current state first
const current = await get_project({project_name: "my-pilot"})
console.log("Current plot:", current.modules.plot)

// Then make informed updates
await update_project({
  project_name: "my-pilot",
  modules: {
    plot: {
      ...current.modules.plot,
      enhanced_element: "New addition based on current state"
    }
  }
})
```

### 2. Use Modular Updates
```javascript
// Instead of updating everything at once, be specific
await update_project({
  project_name: "my-pilot",
  modules: {
    characters: {
      // Only update characters, leave plot unchanged
      protagonist: "Updated protagonist description"
    }
  }
})
```

### 3. Regular Exports for Backup
```javascript
// Weekly backup routine
const allProjects = ["pilot1", "pilot2", "pilot3"]
const backups = {}

for (const project of allProjects) {
  backups[project] = await export_project_json({project_name: project})
}

// Store backups safely...
```

These examples show how Scriptrix MCP tools enable precise, targeted development work! 🎬
