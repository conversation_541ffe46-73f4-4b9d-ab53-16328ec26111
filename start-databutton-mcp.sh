#!/bin/bash

# Start Databutton MCP Server
# This script starts the Databutton MCP server with the API key

echo "Starting Databutton MCP Server..."
echo "API Key file: api-keys/databutton-api-key.txt"
echo "Press Ctrl+C to stop the server"
echo ""

# Check if API key file exists
if [ ! -f "api-keys/databutton-api-key.txt" ]; then
    echo "Error: API key file not found at api-keys/databutton-api-key.txt"
    echo "Please make sure the API key file exists."
    exit 1
fi

# Start the MCP server
uvx databutton-app-mcp@latest -k api-keys/databutton-api-key.txt
