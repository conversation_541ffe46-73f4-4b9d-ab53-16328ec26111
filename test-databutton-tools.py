#!/usr/bin/env python3
"""
Test script to explore what tools are available in the Databutton MCP server.
This script attempts to connect to the MCP server and list available tools.
"""

import asyncio
import json
import websockets
import sys
from pathlib import Path

async def test_mcp_connection():
    """Test connection to Databutton MCP server and list available tools."""
    
    # Read the API key
    api_key_path = Path("api-keys/databutton-api-key.txt")
    if not api_key_path.exists():
        print("❌ API key file not found")
        return False
    
    api_key = api_key_path.read_text().strip()
    
    # The WebSocket URL from the --show-uri command
    uri = "wss://api.databutton.com/_projects/9c68a944-711f-4b04-b971-1480fd9f4385/dbtn/prodx/app/mcp/ws"
    
    try:
        print(f"🔗 Connecting to: {uri}")
        
        # Try connecting with API key in the URI or as a query parameter
        uri_with_key = f"{uri}?api_key={api_key}"

        async with websockets.connect(uri_with_key) as websocket:
            print("✅ Connected to Databutton MCP server!")
            
            # Send MCP initialization message
            init_message = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "initialize",
                "params": {
                    "protocolVersion": "2024-11-05",
                    "capabilities": {
                        "tools": {}
                    },
                    "clientInfo": {
                        "name": "databutton-test-client",
                        "version": "1.0.0"
                    }
                }
            }
            
            await websocket.send(json.dumps(init_message))
            print("📤 Sent initialization message")
            
            # Wait for response
            response = await websocket.recv()
            print("📥 Received response:")
            
            try:
                response_data = json.loads(response)
                print(json.dumps(response_data, indent=2))
                
                # If initialization successful, request tools list
                if "result" in response_data:
                    tools_message = {
                        "jsonrpc": "2.0",
                        "id": 2,
                        "method": "tools/list"
                    }
                    
                    await websocket.send(json.dumps(tools_message))
                    print("\n📤 Requesting tools list...")
                    
                    tools_response = await websocket.recv()
                    print("📥 Tools response:")
                    
                    tools_data = json.loads(tools_response)
                    print(json.dumps(tools_data, indent=2))
                    
                    # Display tools in a user-friendly format
                    if "result" in tools_data and "tools" in tools_data["result"]:
                        tools = tools_data["result"]["tools"]
                        print(f"\n🛠️  Available Tools ({len(tools)}):")
                        print("=" * 50)
                        
                        for i, tool in enumerate(tools, 1):
                            print(f"{i}. {tool.get('name', 'Unknown')}")
                            if 'description' in tool:
                                print(f"   Description: {tool['description']}")
                            if 'inputSchema' in tool:
                                print(f"   Input Schema: {tool['inputSchema']}")
                            print()
                    else:
                        print("🤷 No tools found or unexpected response format")
                        
            except json.JSONDecodeError:
                print(f"❌ Invalid JSON response: {response}")
                
    except websockets.exceptions.ConnectionClosed as e:
        print(f"❌ Connection closed: {e}")
        return False
    except websockets.exceptions.WebSocketException as e:
        print(f"❌ WebSocket error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False
    
    return True

async def main():
    """Main function to run the test."""
    print("🧪 Testing Databutton MCP Server Connection")
    print("=" * 50)
    
    success = await test_mcp_connection()
    
    if success:
        print("\n✅ Test completed successfully!")
    else:
        print("\n❌ Test failed. Check the errors above.")
        return 1
    
    return 0

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(result)
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
        sys.exit(1)
