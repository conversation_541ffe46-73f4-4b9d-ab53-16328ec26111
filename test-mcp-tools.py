#!/usr/bin/env python3
"""
Test the Databutton MCP tools by running the MCP server and sending it commands.
This uses a different approach - running the MCP server as intended.
"""

import subprocess
import json
import sys
import time
from pathlib import Path

def test_mcp_server_tools():
    """Test the MCP server tools by examining what's available."""
    
    print("🔍 Testing Databutton MCP Server Tools")
    print("=" * 50)
    
    # Check if API key exists
    api_key_path = Path("api-keys/databutton-api-key.txt")
    if not api_key_path.exists():
        print("❌ API key file not found")
        return False
    
    print("✅ API key file found")
    
    # Test the MCP server startup
    print("\n🚀 Testing MCP server startup...")
    try:
        # Run the MCP server with verbose output to see what it does
        cmd = ["uvx", "databutton-app-mcp@latest", "-k", "api-keys/databutton-api-key.txt", "-v"]
        
        print(f"Running: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        print(f"Return code: {result.returncode}")
        print(f"STDOUT:\n{result.stdout}")
        print(f"STDERR:\n{result.stderr}")
        
        if result.returncode == 0:
            print("✅ MCP server started successfully")
        else:
            print("❌ MCP server failed to start")
            
    except subprocess.TimeoutExpired:
        print("⏰ MCP server startup timed out (this might be normal - it may be waiting for connections)")
    except Exception as e:
        print(f"❌ Error testing MCP server: {e}")
        return False
    
    # Show what we know about the available tools
    print("\n🛠️  Known Available Tools:")
    print("-" * 40)
    
    tools = [
        ("check_health", "Health check", "No parameters"),
        ("list_projects", "List all TV pilot projects", "No parameters"),
        ("create_project", "Create new pilot project", "Project data required"),
        ("get_project", "Get specific project details", "project_id required"),
        ("update_project", "Update project data", "project_id and data required"),
        ("delete_project", "Delete project", "project_id required"),
        ("export_project_json", "Export project as JSON", "project_id required")
    ]
    
    for i, (name, desc, params) in enumerate(tools, 1):
        print(f"{i}. {name}")
        print(f"   📝 {desc}")
        print(f"   📥 {params}")
        print()
    
    print("🎯 What Your App Does:")
    print("-" * 40)
    print("Based on the available MCP tools, your Databutton app is a")
    print("TV Pilot Project Management System that provides:")
    print()
    print("📺 TV Pilot Management Features:")
    print("  • Create and manage TV pilot projects")
    print("  • Store project details and metadata")
    print("  • Update project information")
    print("  • Delete projects when needed")
    print("  • Export project data as JSON")
    print("  • Health monitoring")
    print()
    print("🏗️  Architecture:")
    print("  • Python backend with API endpoints")
    print("  • Each API endpoint becomes an MCP tool")
    print("  • Likely uses a database to store project data")
    print("  • RESTful API design pattern")
    print()
    print("💡 How to Work with the Code:")
    print("  1. The MCP tools expose your app's functionality")
    print("  2. To modify the actual code, work in Databutton platform")
    print("  3. Each tool corresponds to an API endpoint in your app")
    print("  4. You can use these tools to:")
    print("     - Test your app's functionality")
    print("     - Integrate with AI assistants")
    print("     - Build workflows that use your app")
    print()
    print("🤖 AI Integration Examples:")
    print("  • 'Create a new sci-fi pilot project called StarQuest'")
    print("  • 'List all comedy pilot projects'")
    print("  • 'Export the project data for project ID 5'")
    print("  • 'Update the budget for the drama pilot'")
    
    return True

def show_next_steps():
    """Show what the user can do next."""
    print("\n🚀 Next Steps:")
    print("-" * 40)
    print("1. 🖥️  Set up Claude Desktop:")
    print("   • Install Claude Desktop")
    print("   • Add the MCP configuration from mcp-config.json")
    print("   • Ask Claude to use your TV pilot management tools")
    print()
    print("2. 🛠️  Test individual tools:")
    print("   • Use Claude to call 'check_health'")
    print("   • Ask Claude to 'list_projects'")
    print("   • Have Claude create a test project")
    print()
    print("3. 🎬 Work with your app:")
    print("   • Ask Claude: 'Create a new TV pilot project for a comedy series'")
    print("   • Ask Claude: 'Show me all the projects and their details'")
    print("   • Ask Claude: 'Export project 1 as JSON'")
    print()
    print("4. 🔧 Modify the code:")
    print("   • Go to your Databutton app in the browser")
    print("   • Modify the API endpoints")
    print("   • Redeploy to update the MCP tools")

def main():
    """Main function."""
    success = test_mcp_server_tools()
    
    if success:
        show_next_steps()
        print("\n✅ MCP server exploration completed!")
        return 0
    else:
        print("\n❌ MCP server exploration failed")
        return 1

if __name__ == "__main__":
    try:
        result = main()
        sys.exit(result)
    except KeyboardInterrupt:
        print("\n🛑 Interrupted by user")
        sys.exit(1)
