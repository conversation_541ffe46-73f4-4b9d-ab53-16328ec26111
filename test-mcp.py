#!/usr/bin/env python3
"""
Simple test script to verify the Databutton MCP server setup.
This script checks if the MCP server can be started and responds correctly.
"""

import subprocess
import sys
import os
import time

def check_api_key():
    """Check if the API key file exists."""
    api_key_path = "api-keys/databutton-api-key.txt"
    if not os.path.exists(api_key_path):
        print("❌ API key file not found at:", api_key_path)
        return False
    
    print("✅ API key file found")
    return True

def check_uvx():
    """Check if uvx is available."""
    try:
        result = subprocess.run(["uvx", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ uvx is available")
            return True
        else:
            print("❌ uvx is not working properly")
            return False
    except FileNotFoundError:
        print("❌ uvx not found. Please install uv first: pipx install uv")
        return False

def test_mcp_server():
    """Test if the MCP server can be started."""
    print("🧪 Testing MCP server startup...")
    
    try:
        # Try to start the MCP server with a timeout
        cmd = ["uvx", "databutton-app-mcp@latest", "-k", "api-keys/databutton-api-key.txt"]
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        # Wait a bit for the server to start
        time.sleep(3)
        
        # Check if process is still running or has completed
        if process.poll() is None:
            print("✅ MCP server started successfully (still running)")
            process.terminate()
            process.wait()
            return True
        else:
            stdout, stderr = process.communicate()
            # MCP servers typically exit after installation when not connected to a client
            if "Installed" in stderr and process.returncode == 0:
                print("✅ MCP server dependencies installed successfully")
                print("   (Server exits normally when not connected to MCP client)")
                return True
            elif process.returncode == 0:
                print("✅ MCP server setup completed successfully")
                return True
            else:
                print("❌ MCP server failed to start")
                print("STDOUT:", stdout)
                print("STDERR:", stderr)
                return False
                
    except Exception as e:
        print(f"❌ Error testing MCP server: {e}")
        return False

def main():
    """Run all tests."""
    print("🔍 Testing Databutton MCP Setup")
    print("=" * 40)
    
    tests = [
        ("API Key File", check_api_key),
        ("uvx Availability", check_uvx),
        ("MCP Server", test_mcp_server),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}:")
        result = test_func()
        results.append(result)
    
    print("\n" + "=" * 40)
    print("📊 Test Results:")
    
    all_passed = all(results)
    if all_passed:
        print("🎉 All tests passed! Your Databutton MCP setup is ready to use.")
        print("\n💡 Next steps:")
        print("   - Use './start-databutton-mcp.sh' to start the server")
        print("   - Configure your MCP client with the provided mcp-config.json")
        print("   - See MCP-SETUP.md for detailed instructions")
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
