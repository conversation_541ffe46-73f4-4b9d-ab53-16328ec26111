#!/usr/bin/env python3
"""
Test Scriptrix MCP tools for targeted development and UI fixes.
This will test the get_project, update_project, and export_project_json tools.
"""

import asyncio
import json
import websockets
import sys
from pathlib import Path

async def test_scriptrix_tools():
    """Test the Scriptrix MCP tools for targeted development."""
    
    print("🎬 Testing Scriptrix MCP Tools")
    print("=" * 50)
    
    # Read API key
    api_key_path = Path("api-keys/databutton-api-key.txt")
    if not api_key_path.exists():
        print("❌ API key file not found")
        return False
    
    api_key = api_key_path.read_text().strip()
    uri = "wss://api.databutton.com/_projects/9c68a944-711f-4b04-b971-1480fd9f4385/dbtn/prodx/app/mcp/ws"
    
    try:
        # Try different auth methods
        auth_methods = [
            ("Query Parameter", f"{uri}?api_key={api_key}"),
            ("Token Parameter", f"{uri}?token={api_key}"),
            ("Auth Parameter", f"{uri}?auth={api_key}")
        ]
        
        for method_name, test_uri in auth_methods:
            print(f"\n🔄 Trying {method_name} authentication...")
            
            try:
                async with websockets.connect(test_uri) as websocket:
                    print(f"✅ Connected with {method_name}!")
                    
                    # Initialize MCP
                    init_msg = {
                        "jsonrpc": "2.0", "id": 1, "method": "initialize",
                        "params": {
                            "protocolVersion": "2024-11-05",
                            "capabilities": {"tools": {}},
                            "clientInfo": {"name": "scriptrix-tester", "version": "1.0.0"}
                        }
                    }
                    
                    await websocket.send(json.dumps(init_msg))
                    init_response = await websocket.recv()
                    init_result = json.loads(init_response)
                    
                    if "result" not in init_result:
                        print("❌ MCP initialization failed")
                        continue
                    
                    print("✅ MCP initialized successfully")
                    
                    # Get available tools
                    tools_msg = {"jsonrpc": "2.0", "id": 2, "method": "tools/list"}
                    await websocket.send(json.dumps(tools_msg))
                    tools_response = await websocket.recv()
                    tools_data = json.loads(tools_response)
                    
                    if "result" in tools_data and "tools" in tools_data["result"]:
                        tools = tools_data["result"]["tools"]
                        print(f"\n🛠️  Available Scriptrix Tools ({len(tools)}):")
                        print("-" * 40)
                        
                        for tool in tools:
                            name = tool.get("name", "unknown")
                            desc = tool.get("description", "No description")
                            print(f"📋 {name}")
                            print(f"   {desc}")
                            
                            # Show input schema
                            if "inputSchema" in tool:
                                schema = tool["inputSchema"]
                                if "properties" in schema:
                                    print("   📥 Parameters:")
                                    for prop, details in schema["properties"].items():
                                        prop_type = details.get("type", "unknown")
                                        prop_desc = details.get("description", "")
                                        print(f"      • {prop} ({prop_type}): {prop_desc}")
                            print()
                        
                        # Test the tools
                        await test_individual_tools(websocket, tools)
                        return True
                    
            except websockets.exceptions.WebSocketException as e:
                print(f"❌ WebSocket error with {method_name}: {e}")
                continue
            except Exception as e:
                print(f"❌ Error with {method_name}: {e}")
                continue
        
        print("❌ All authentication methods failed")
        return False
        
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

async def test_individual_tools(websocket, tools):
    """Test each Scriptrix tool individually."""
    
    print("🧪 Testing Individual Tools")
    print("-" * 40)
    
    tool_names = [tool.get("name") for tool in tools]
    
    # Test 1: Get project (inspect current state)
    if "get_project" in tool_names:
        print("1️⃣ Testing get_project...")
        get_call = {
            "jsonrpc": "2.0", "id": 10, "method": "tools/call",
            "params": {
                "name": "get_project",
                "arguments": {"project_name": "test-pilot"}  # Try with a test name
            }
        }
        
        await websocket.send(json.dumps(get_call))
        get_response = await websocket.recv()
        get_result = json.loads(get_response)
        
        print("📄 get_project result:")
        if "result" in get_result:
            print(json.dumps(get_result["result"], indent=2))
        else:
            print(json.dumps(get_result, indent=2))
        print()
    
    # Test 2: Export project JSON (for analysis)
    if "export_project_json" in tool_names:
        print("2️⃣ Testing export_project_json...")
        export_call = {
            "jsonrpc": "2.0", "id": 11, "method": "tools/call",
            "params": {
                "name": "export_project_json",
                "arguments": {"project_name": "test-pilot"}
            }
        }
        
        await websocket.send(json.dumps(export_call))
        export_response = await websocket.recv()
        export_result = json.loads(export_response)
        
        print("📦 export_project_json result:")
        if "result" in export_result:
            print(json.dumps(export_result["result"], indent=2))
        else:
            print(json.dumps(export_result, indent=2))
        print()
    
    # Test 3: Update project (targeted modifications)
    if "update_project" in tool_names:
        print("3️⃣ Testing update_project...")
        update_call = {
            "jsonrpc": "2.0", "id": 12, "method": "tools/call",
            "params": {
                "name": "update_project",
                "arguments": {
                    "project_name": "test-pilot",
                    "modules": {
                        "plot": {
                            "trajectory": "Updated character arc for testing",
                            "characters": "Test character refinement"
                        }
                    }
                }
            }
        }
        
        await websocket.send(json.dumps(update_call))
        update_response = await websocket.recv()
        update_result = json.loads(update_response)
        
        print("✏️  update_project result:")
        if "result" in update_result:
            print(json.dumps(update_result["result"], indent=2))
        else:
            print(json.dumps(update_result, indent=2))
        print()

def show_vscode_setup():
    """Show how to set up VS Code with Continue.dev for MCP."""
    
    print("\n🔧 VS Code MCP Setup")
    print("=" * 50)
    
    print("1️⃣ Install Continue.dev Extension:")
    print("   • Open VS Code Extensions (Ctrl+Shift+X)")
    print("   • Search for 'Continue'")
    print("   • Install the Continue extension")
    print()
    
    print("2️⃣ Configure MCP in VS Code settings.json:")
    vscode_config = {
        "continue.mcp.servers": [
            {
                "name": "scriptrix-fixes",
                "url": "https://api.databutton.com/_projects/9c68a944-711f-4b04-b971-1480fd9f4385/dbtn/prodx/app/routes",
                "description": "Scriptrix targeted fixes and updates"
            }
        ]
    }
    print("   Add this to your VS Code settings.json:")
    print(json.dumps(vscode_config, indent=4))
    print()
    
    print("3️⃣ Example MCP Commands in VS Code:")
    print("   • Get project state: get_project({project_name: 'my-pilot'})")
    print("   • Update content: update_project({project_name: 'my-pilot', modules: {...}})")
    print("   • Export data: export_project_json({project_name: 'my-pilot'})")
    print()
    
    print("4️⃣ Benefits for Development:")
    print("   ✅ Precision - Target exact fields/modules")
    print("   ✅ Speed - No full app rebuilds")
    print("   ✅ Batch operations - Multiple fixes at once")
    print("   ✅ State inspection - See current data before changes")
    print("   ✅ Rollback friendly - Easy to revert specific changes")

async def main():
    """Main function."""
    success = await test_scriptrix_tools()
    
    if success:
        show_vscode_setup()
        print("\n✅ Scriptrix MCP testing completed!")
        print("\n💡 Next: Set up VS Code with Continue.dev to use these tools for targeted development!")
    else:
        print("\n❌ Scriptrix MCP testing failed")
        print("💡 The tools are available, but we need proper authentication.")
        print("Try using Claude Desktop or another MCP client with the configuration.")
    
    return 0 if success else 1

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(result)
    except KeyboardInterrupt:
        print("\n🛑 Interrupted by user")
        sys.exit(1)
